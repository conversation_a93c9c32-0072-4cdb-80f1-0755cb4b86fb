version: '3.8'

services:
  rabbitmq:
    image: rabbitmq:3-management
    container_name: rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmqctl", "status"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - order-network

  store-order:
    build:
      context: ./src/store-order
      dockerfile: Dockerfile
    container_name: store-order
    ports:
      - "5000:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - RabbitMq__Host=rabbitmq
      - RabbitMq__Port=5672
      - RabbitMq__Username=guest
      - RabbitMq__Password=guest
      - RabbitMq__OrderExchange=order.exchange
      - RabbitMq__OrderQueue=order.queue
      - RabbitMq__StatusExchange=order.status.exchange
      - RabbitMq__StatusUpdateQueue=order.status.update.queue
    depends_on:
      rabbitmq:
        condition: service_healthy
    networks:
      - order-network

  warehouse-order:
    build:
      context: ./src/warehouse-order
      dockerfile: Dockerfile
    container_name: warehouse-order
    ports:
      - "8080:8080"
    environment:
      - RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672/
      - ORDER_QUEUE=order.queue
      - STATUS_UPDATE_QUEUE=order.status.update.queue
      - ORDER_EXCHANGE=order.exchange
      - STATUS_EXCHANGE=order.status.exchange
      - HTTP_PORT=8080
    depends_on:
      rabbitmq:
        condition: service_healthy
    networks:
      - order-network

networks:
  order-network:
    driver: bridge

volumes:
  rabbitmq_data:
