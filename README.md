# Order Management System

Bu proje, Store ve Warehouse servisleri arasında RabbitMQ üzerinden iletişim kuran bir order management sistemidir.

## Sistem Mimarisi

- **Store Order Service** (.NET 9): Order oluşturma ve status güncellemelerini consume etme
- **Warehouse Order Service** (Go): Order'ları consume etme ve HTTP endpoint üzerinden status güncelleme
- **RabbitMQ**: Servisler arası mesajlaşma

## Sistem Akışı

1. **Store Service** → Order oluşturur ve RabbitMQ'ya gönderir
2. **Warehouse Service** → Order'ı RabbitMQ'dan consume eder
3. **HTTP Endpoint** → Warehouse'da manuel status güncelleme yapılır
4. **Warehouse Service** → Status güncellemesini RabbitMQ'ya gönderir
5. **Store Service** → Status güncellemesini RabbitMQ'dan consume eder

## Gereksinimler

- .NET 9 SDK
- Go 1.23+
- Docker (RabbitMQ için)

## Kurulum ve Çalıştırma

### 1. RabbitMQ'yu <PERSON>n

```bash
docker-compose up -d
```

RabbitMQ Management UI: http://localhost:15672 (guest/guest)

### 2. Go Warehouse Service'i Başlatın

```bash
cd src/warehouse-order
go mod tidy
go run cmd/api/main.go
```

### 3. C# Store Service'i Başlatın

```bash
cd src/store-order/store-order/store-order
dotnet restore
dotnet run
```

### 4. Test Edin

Sipariş oluşturmak için:

```bash
curl -X POST http://localhost:5000/orders \
  -H "Content-Type: application/json" \
  -d '{
    "customerName": "John Doe",
    "totalAmount": 299.99
  }'
```

## Mesaj Akışı

### Order Creation (C# → Go)
- **Exchange**: `order.exchange`
- **Queue**: `order.queue`
- **Message**: `OrderCreatedDto`

### Status Update (Go → C#)
- **Exchange**: `order.status.exchange`
- **Queue**: `order.status.update.queue`
- **Message**: `OrderStatusUpdateDto`

## Proje Yapısı

```
src/
├── store-order/          # C# .NET 9 Web API
│   └── store-order/
│       ├── StoreOrder.Api/
│       ├── StoreOrder.Application/
│       ├── StoreOrder.Domain/
│       ├── StoreOrder.Infrastructure/
│       └── store-order/  # Main API project
└── warehouse-order/      # Go Service
    ├── cmd/api/          # Main application
    ├── internal/
    │   ├── config/       # Configuration
    │   ├── models/       # Data models
    │   ├── orders/       # Order processing logic
    │   └── rabbitmq/     # RabbitMQ connection
    └── pkg/              # Shared packages
```

## Teknolojiler

- **C#**: .NET 9, MassTransit, RabbitMQ
- **Go**: Go 1.23, AMQP library
- **Message Broker**: RabbitMQ
- **Containerization**: Docker
