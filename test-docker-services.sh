#!/bin/bash

echo "🚀 Starting Docker services..."
docker-compose up -d

echo "⏳ Waiting for services to be ready..."
sleep 30

echo "📋 Checking service status..."
docker-compose ps

echo ""
echo "🔍 Testing Store Order Service..."
echo "Creating a new order..."

# Test 1: Create an order
ORDER_RESPONSE=$(curl -s -X POST http://localhost:5000/orders \
  -H "Content-Type: application/json" \
  -d '{
    "customerName": "John Doe",
    "totalAmount": 99.99
  }')

echo "Order creation response: $ORDER_RESPONSE"

# Extract order ID from response
ORDER_ID=$(echo $ORDER_RESPONSE | grep -o '"orderId":"[^"]*' | cut -d'"' -f4)
echo "Order ID: $ORDER_ID"

echo ""
echo "⏳ Waiting for order to be consumed by warehouse..."
sleep 5

echo ""
echo "🏭 Testing Warehouse Status Update..."

# Test 2: Update order status to Processing
echo "Updating order status to Processing..."
curl -X POST http://localhost:8080/orders/status \
  -H "Content-Type: application/json" \
  -d "{
    \"order_id\": \"$ORDER_ID\",
    \"status\": \"Processing\",
    \"message\": \"Order is being processed in warehouse\"
  }"

echo -e "\n"
echo "⏳ Waiting for status update to be processed..."
sleep 3

# Test 3: Update order status to Shipped
echo "Updating order status to Shipped..."
curl -X POST http://localhost:8080/orders/status \
  -H "Content-Type: application/json" \
  -d "{
    \"order_id\": \"$ORDER_ID\",
    \"status\": \"Shipped\",
    \"message\": \"Order has been shipped from warehouse\"
  }"

echo -e "\n"
echo "⏳ Waiting for status update to be processed..."
sleep 3

# Test 4: Update order status to Delivered
echo "Updating order status to Delivered..."
curl -X POST http://localhost:8080/orders/status \
  -H "Content-Type: application/json" \
  -d "{
    \"order_id\": \"$ORDER_ID\",
    \"status\": \"Delivered\",
    \"message\": \"Order has been delivered to customer\"
  }"

echo -e "\n\n"
echo "📊 Checking service logs..."
echo "=== Store Order Service Logs ==="
docker-compose logs --tail=20 store-order

echo ""
echo "=== Warehouse Order Service Logs ==="
docker-compose logs --tail=20 warehouse-order

echo ""
echo "✅ Test completed!"
echo ""
echo "🔗 Useful URLs:"
echo "- Store Order API: http://localhost:5000"
echo "- Warehouse Order API: http://localhost:8080"
echo "- RabbitMQ Management: http://localhost:15672 (guest/guest)"
echo ""
echo "To stop services: docker-compose down"
