#!/bin/bash

echo "Testing Warehouse Status Update Endpoint..."

# Test 1: Valid status update
echo "Test 1: Valid status update"
curl -X POST http://localhost:8080/orders/status \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "12345",
    "status": "Processing",
    "message": "Order is being processed in warehouse"
  }'

echo -e "\n\n"

# Test 2: Another status update
echo "Test 2: Status update to Shipped"
curl -X POST http://localhost:8080/orders/status \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "12345",
    "status": "Shipped",
    "message": "Order has been shipped"
  }'

echo -e "\n\n"

# Test 3: Invalid status
echo "Test 3: Invalid status (should fail)"
curl -X POST http://localhost:8080/orders/status \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "12345",
    "status": "InvalidStatus",
    "message": "This should fail"
  }'

echo -e "\n\n"

# Test 4: Missing required fields
echo "Test 4: Missing order_id (should fail)"
curl -X POST http://localhost:8080/orders/status \
  -H "Content-Type: application/json" \
  -d '{
    "status": "Processing",
    "message": "Missing order_id"
  }'

echo -e "\n\nTest completed!"
