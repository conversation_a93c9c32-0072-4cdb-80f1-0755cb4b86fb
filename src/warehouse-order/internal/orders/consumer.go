package orders

import (
	"encoding/json"
	"log"
	"warehouse-order/internal/models"

	"github.com/streadway/amqp"
)

type Service struct {
	repository *Repository
	publisher  *Publisher
}

func NewService(repository *Repository, publisher *Publisher) *Service {
	return &Service{
		repository: repository,
		publisher:  publisher,
	}
}

func (s *Service) ConsumeOrders(ch *amqp.Channel, queue string) {
	msgs, err := ch.Consume(
		queue,
		"",
		false, // manual ack
		false,
		false,
		false,
		nil)

	if err != nil {
		log.Fatalf("Failed to register a consumer: %s", err)
	}

	log.Printf("Waiting for orders on queue: %s", queue)

	go func() {
		for d := range msgs {
			var order models.OrderCreated
			if err := json.Unmarshal(d.Body, &order); err != nil {
				log.Printf("Failed to unmarshal JSON: %s", err)
				d.Nack(false, false) // reject message
				continue
			}

			log.Printf("[Go Consumer] Order received: %+v", order)

			// Save order to repository
			if err := s.repository.SaveOrder(&order); err != nil {
				log.Printf("Failed to save order: %s", err)
				d.Nack(false, true) // requeue message
				continue
			}

			log.Printf("[Warehouse] Order %s received and saved. Use HTTP endpoint to update status.", order.OrderID)

			// Acknowledge message
			d.Ack(false)
		}
	}()
}
