package handlers

import (
	"encoding/json"
	"log"
	"net/http"
	"warehouse-order/internal/models"
	"warehouse-order/internal/orders"

	"github.com/gorilla/mux"
)

type StatusHandler struct {
	publisher *orders.Publisher
}

func NewStatusHandler(publisher *orders.Publisher) *StatusHandler {
	return &StatusHandler{
		publisher: publisher,
	}
}

type UpdateStatusRequest struct {
	OrderID string `json:"order_id"`
	Status  string `json:"status"`
	Message string `json:"message,omitempty"`
}

func (h *StatusHandler) UpdateOrderStatus(w http.ResponseWriter, r *http.Request) {
	var req UpdateStatusRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// Validate required fields
	if req.OrderID == "" || req.Status == "" {
		http.Error(w, "order_id and status are required", http.StatusBadRequest)
		return
	}

	// Validate status
	var orderStatus models.OrderStatus
	switch req.Status {
	case "Pending":
		orderStatus = models.OrderStatusPending
	case "Processing":
		orderStatus = models.OrderStatusProcessing
	case "Shipped":
		orderStatus = models.OrderStatusShipped
	case "Delivered":
		orderStatus = models.OrderStatusDelivered
	case "Cancelled":
		orderStatus = models.OrderStatusCancelled
	default:
		http.Error(w, "Invalid status", http.StatusBadRequest)
		return
	}

	// Create status update
	statusUpdate := models.OrderStatusUpdate{
		OrderID:   req.OrderID,
		Status:    orderStatus,
		UpdatedAt: models.GetCurrentTimeString(),
		Message:   req.Message,
	}

	// Publish status update to RabbitMQ
	if err := h.publisher.PublishStatusUpdate(statusUpdate); err != nil {
		log.Printf("Failed to publish status update: %s", err)
		http.Error(w, "Failed to publish status update", http.StatusInternalServerError)
		return
	}

	log.Printf("[Warehouse HTTP] Status updated: OrderID=%s, Status=%s", req.OrderID, req.Status)

	// Return success response
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{
		"message": "Status updated successfully",
		"order_id": req.OrderID,
		"status": req.Status,
	})
}

func (h *StatusHandler) RegisterRoutes(router *mux.Router) {
	router.HandleFunc("/orders/status", h.UpdateOrderStatus).Methods("POST")
}
