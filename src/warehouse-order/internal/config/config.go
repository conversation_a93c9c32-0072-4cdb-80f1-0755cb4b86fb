package config

import (
	"os"
)

type Config struct {
	RabbitMQ RabbitMQConfig
	HTTP     HTTPConfig
}

type RabbitMQConfig struct {
	URL               string
	OrderQueue        string
	StatusUpdateQueue string
	OrderExchange     string
	StatusExchange    string
}

type HTTPConfig struct {
	Port string
}

func Load() *Config {
	return &Config{
		RabbitMQ: RabbitMQConfig{
			URL:               getEnv("RABBITMQ_URL", "amqp://guest:guest@localhost:5672/"),
			OrderQueue:        getEnv("ORDER_QUEUE", "order.queue"),
			StatusUpdateQueue: getEnv("STATUS_UPDATE_QUEUE", "order.status.update.queue"),
			OrderExchange:     getEnv("ORDER_EXCHANGE", "order.exchange"),
			StatusExchange:    getEnv("STATUS_EXCHANGE", "order.status.exchange"),
		},
		HTTP: HTTPConfig{
			Port: getEnv("HTTP_PORT", "8080"),
		},
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
