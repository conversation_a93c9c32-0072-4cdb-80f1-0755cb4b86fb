package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"
	"warehouse-order/internal/config"
	"warehouse-order/internal/handlers"
	"warehouse-order/internal/orders"
	"warehouse-order/internal/rabbitmq"
	"warehouse-order/internal/server"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Connect to RabbitMQ
	conn, ch := rabbitmq.Connect(cfg.RabbitMQ.URL)
	defer conn.Close()
	defer ch.Close()

	// Setup queues and exchanges
	if err := rabbitmq.SetupQueuesAndExchanges(ch, cfg); err != nil {
		log.Fatalf("Failed to setup RabbitMQ: %s", err)
	}

	// Initialize repository and publisher
	repository := orders.NewRepository()
	publisher := orders.NewPublisher(ch, cfg.RabbitMQ.StatusExchange)

	// Initialize order service
	orderService := orders.NewService(repository, publisher)

	// Start consuming orders
	orderService.ConsumeOrders(ch, cfg.RabbitMQ.OrderQueue)

	// Initialize HTTP handlers and server
	statusHandler := handlers.NewStatusHandler(publisher)
	httpServer := server.NewServer(cfg.HTTP.Port, statusHandler)

	// Start HTTP server in a goroutine
	go func() {
		if err := httpServer.Start(); err != nil {
			log.Printf("HTTP server error: %s", err)
		}
	}()

	log.Printf("Warehouse Order Service started:")
	log.Printf("- RabbitMQ consumer listening on queue: %s", cfg.RabbitMQ.OrderQueue)
	log.Printf("- HTTP server listening on port: %s", cfg.HTTP.Port)

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Warehouse Order Service shutting down...")

	// Graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := httpServer.Shutdown(ctx); err != nil {
		log.Printf("HTTP server shutdown error: %s", err)
	}
}
