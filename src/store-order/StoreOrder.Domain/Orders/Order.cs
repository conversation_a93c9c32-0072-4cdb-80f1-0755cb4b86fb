namespace StoreOrder.Domain.Orders;

public sealed class Order
{
    public Guid Id { get; private set; }
    public string CustomerName { get; private set; }
    public decimal TotalAmount { get; private set; }

    private Order(Guid id, string customerName, decimal totalAmount)
    {
        Id = id;
        CustomerName = customerName;
        TotalAmount = totalAmount;
    }

    public static Order Create(string customerName, decimal totalAmount)
    {
        var id = Guid.NewGuid();
        return new Order(id, customerName, totalAmount);
    }
}
