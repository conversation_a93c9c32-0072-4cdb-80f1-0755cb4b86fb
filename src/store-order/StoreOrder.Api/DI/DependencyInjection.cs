using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using StoreOrder.Application.Common;
using StoreOrder.Infrastructure.Messaging;

namespace StoreOrder.Api.DI;

public static class DependencyInjection
{
    public static IServiceCollection AddPresentation(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddApplication();
        services.AddMessaging(configuration);
        return services;
    }
}
