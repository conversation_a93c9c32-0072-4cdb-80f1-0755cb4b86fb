using MassTransit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using StoreOrder.Application.Common.Messaging;
using StoreOrder.Application.Orders.Events;

namespace StoreOrder.Infrastructure.Messaging;

public static class MassTransitSetup
{
    public static IServiceCollection AddMessaging(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<RabbitMqConfig>(options => configuration.GetSection("RabbitMq").Bind(options));

        services.AddScoped<IOrderPublisher, RabbitMqPublisher>();

        services.AddMassTransit(x =>
        {
            x.AddConsumer<OrderStatusUpdatedConsumer>();

            x.UsingRabbitMq((ctx, cfg) =>
            {
                var rabbitCfg = configuration.GetSection("RabbitMq").Get<RabbitMqConfig>()!;

                cfg.Host(rabbitCfg.Host, "/", h =>
                {
                    h.Username(rabbitCfg.Username);
                    h.Password(rabbitCfg.Password);
                });

                // Interop with non-MassTransit (Go) via raw JSON and string enums
                cfg.UseRawJsonSerializer();

                // Publish topology for OrderCreatedIntegrationEvent to a direct exchange used by Go
                cfg.Message<OrderCreatedIntegrationEvent>(x => x.SetEntityName(rabbitCfg.OrderExchange));
                cfg.Publish<OrderCreatedIntegrationEvent>(p =>
                {
                    p.ExchangeType = "direct";
                });

                // Consume status updates from the queue bound (by Go) to status exchange
                cfg.ReceiveEndpoint(rabbitCfg.StatusUpdateQueue, e =>
                {
                    e.ConfigureConsumer<OrderStatusUpdatedConsumer>(ctx);
                });
            });
        });

        return services;
    }
}
