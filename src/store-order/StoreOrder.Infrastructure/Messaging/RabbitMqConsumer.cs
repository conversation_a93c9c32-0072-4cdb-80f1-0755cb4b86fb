using MassTransit;
using Microsoft.Extensions.Logging;
using StoreOrder.Application.Orders.Events;

namespace StoreOrder.Infrastructure.Messaging;

public sealed class OrderStatusUpdatedConsumer : IConsumer<OrderStatusUpdatedIntegrationEvent>
{
    private readonly ILogger<OrderStatusUpdatedConsumer> _logger;

    public OrderStatusUpdatedConsumer(ILogger<OrderStatusUpdatedConsumer> logger)
    {
        _logger = logger;
    }

    public Task Consume(ConsumeContext<OrderStatusUpdatedIntegrationEvent> context)
    {
        var m = context.Message;
        _logger.LogInformation("[Consumer] Status update received: {OrderId} -> {Status}", m.OrderId, m.Status);
        // TODO: persist/update order status via repository
        return Task.CompletedTask;
    }
}