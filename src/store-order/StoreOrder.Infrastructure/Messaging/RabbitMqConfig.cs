namespace StoreOrder.Infrastructure.Messaging;

public sealed class RabbitMqConfig
{
    public string Host { get; set; } = "localhost";
    public int Port { get; set; } = 5672;
    public string Username { get; set; } = "guest";
    public string Password { get; set; } = "guest";

    public string OrderExchange { get; set; } = "order.exchange";
    public string OrderQueue { get; set; } = "order.queue";

    public string StatusExchange { get; set; } = "order.status.exchange";
    public string StatusUpdateQueue { get; set; } = "order.status.update.queue";
}