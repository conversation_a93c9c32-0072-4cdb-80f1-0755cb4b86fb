﻿"restore":{"projectUniqueName":"/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/StoreOrder.Infrastructure/StoreOrder.Infrastructure.csproj","projectName":"StoreOrder.Infrastructure","projectPath":"/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/StoreOrder.Infrastructure/StoreOrder.Infrastructure.csproj","outputPath":"/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/StoreOrder.Infrastructure/obj/","projectStyle":"PackageReference","originalTargetFrameworks":["net9.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{"/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/StoreOrder.Application/StoreOrder.Application.csproj":{"projectPath":"/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/StoreOrder.Application/StoreOrder.Application.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"MassTransit":{"target":"Package","version":"[8.5.2, )"},"MassTransit.RabbitMQ":{"target":"Package","version":"[8.5.2, )"},"Microsoft.Extensions.Configuration":{"target":"Package","version":"[10.0.0-preview.6.25358.103, )"},"Microsoft.Extensions.Configuration.Binder":{"target":"Package","version":"[10.0.0-preview.6.25358.103, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"/usr/local/share/dotnet/sdk/9.0.300/PortableRuntimeIdentifierGraph.json"}}