# Build stage
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build

WORKDIR /src

# Copy solution and project files
COPY store-order.sln ./
COPY store-order/StoreOrder.Api/StoreOrder.Api.csproj ./store-order/StoreOrder.Api/
COPY store-order/StoreOrder.Application/StoreOrder.Application.csproj ./store-order/StoreOrder.Application/
COPY store-order/StoreOrder.Domain/StoreOrder.Domain.csproj ./store-order/StoreOrder.Domain/
COPY store-order/StoreOrder.Infrastructure/StoreOrder.Infrastructure.csproj ./store-order/StoreOrder.Infrastructure/

# Restore dependencies
RUN dotnet restore

# Copy source code
COPY . .

# Build the application
WORKDIR /src/store-order/StoreOrder.Api
RUN dotnet build -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish -c Release -o /app/publish /p:UseAppHost=false

# Runtime stage
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS final

WORKDIR /app

# Copy published app
COPY --from=publish /app/publish .

# Expose port
EXPOSE 8080

ENTRYPOINT ["dotnet", "StoreOrder.Api.dll"]
