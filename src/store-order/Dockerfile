# Build stage
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build

WORKDIR /src

# Copy solution and project files
COPY store-order.sln ./
COPY StoreOrder.Api/StoreOrder.Api.csproj ./StoreOrder.Api/
COPY StoreOrder.Application/StoreOrder.Application.csproj ./StoreOrder.Application/
COPY StoreOrder.Domain/StoreOrder.Domain.csproj ./StoreOrder.Domain/
COPY StoreOrder.Infrastructure/StoreOrder.Infrastructure.csproj ./StoreOrder.Infrastructure/

# Restore dependencies
RUN dotnet restore

# Copy source code
COPY . .

# Build the application
WORKDIR /src/StoreOrder.Api
RUN dotnet build -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish -c Release -o /app/publish /p:UseAppHost=false

# Runtime stage
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS final

WORKDIR /app

# Copy published app
COPY --from=publish /app/publish .

# Expose port
EXPOSE 8080

ENTRYPOINT ["dotnet", "StoreOrder.Api.dll"]
