using MediatR;
using StoreOrder.Application.Orders.Commands.CreateOrder;

namespace StoreOrder.Api.Endpoints;

public static class OrderEndpoints
{
    public static IEndpointRouteBuilder MapOrderEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/orders");

        group.MapPost("/", async (CreateOrderRequest request, ISender sender) =>
        {
            var id = await sender.Send(new CreateOrderCommand(request.CustomerName, request.TotalAmount));
            return Results.Ok(new { orderId = id });
        });

        group.MapPost("/status", (UpdateOrderStatusRequest request, ILogger<Program> logger) =>
        {
            logger.LogInformation(
                "[Store Order] Status update received via HTTP: OrderId={OrderId}, Status={Status}, Message={Message}",
                request.OrderId,
                request.Status,
                request.Message);

            return Results.Ok(new { message = "Status received successfully" });
        });

        return app;
    }
}

public sealed record CreateOrderRequest(string CustomerName, decimal TotalAmount);