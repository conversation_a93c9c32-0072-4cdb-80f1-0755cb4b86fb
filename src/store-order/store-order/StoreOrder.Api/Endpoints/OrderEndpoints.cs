using MediatR;
using StoreOrder.Application.Orders.Commands.CreateOrder;

namespace StoreOrder.Api.Endpoints;

public static class OrderEndpoints
{
    public static IEndpointRouteBuilder MapOrderEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/orders");

        group.MapPost("/", async (CreateOrderRequest request, ISender sender) =>
        {
            var id = await sender.Send(new CreateOrderCommand(request.CustomerName, request.TotalAmount));
            return Results.Ok(new { orderId = id });
        });

        return app;
    }
}

public sealed record CreateOrderRequest(string CustomerName, decimal TotalAmount);
public sealed record UpdateOrderStatusRequest(string OrderId, string Status, string? Message);