{"version": 2, "dgSpecHash": "e6qDdoEiqAM=", "success": true, "projectFilePath": "/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Api/StoreOrder.Api.csproj", "expectedPackageFiles": ["/Users/<USER>/.nuget/packages/masstransit/8.5.2/masstransit.8.5.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/masstransit.abstractions/8.5.2/masstransit.abstractions.8.5.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/masstransit.rabbitmq/8.5.2/masstransit.rabbitmq.8.5.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/mediatr/13.0.0/mediatr.13.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/mediatr.contracts/2.0.1/mediatr.contracts.2.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration/10.0.0-preview.6.25358.103/microsoft.extensions.configuration.10.0.0-preview.6.25358.103.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.abstractions/10.0.0-preview.6.25358.103/microsoft.extensions.configuration.abstractions.10.0.0-preview.6.25358.103.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.binder/10.0.0-preview.6.25358.103/microsoft.extensions.configuration.binder.10.0.0-preview.6.25358.103.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection/10.0.0-preview.6.25358.103/microsoft.extensions.dependencyinjection.10.0.0-preview.6.25358.103.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/10.0.0-preview.6.25358.103/microsoft.extensions.dependencyinjection.abstractions.10.0.0-preview.6.25358.103.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.diagnostics.abstractions/9.0.0/microsoft.extensions.diagnostics.abstractions.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.diagnostics.healthchecks/9.0.0/microsoft.extensions.diagnostics.healthchecks.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.diagnostics.healthchecks.abstractions/9.0.0/microsoft.extensions.diagnostics.healthchecks.abstractions.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.fileproviders.abstractions/9.0.0/microsoft.extensions.fileproviders.abstractions.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.hosting.abstractions/9.0.0/microsoft.extensions.hosting.abstractions.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging/8.0.0/microsoft.extensions.logging.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/9.0.0/microsoft.extensions.logging.abstractions.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.options/9.0.0/microsoft.extensions.options.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.primitives/10.0.0-preview.6.25358.103/microsoft.extensions.primitives.10.0.0-preview.6.25358.103.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.abstractions/8.0.1/microsoft.identitymodel.abstractions.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.jsonwebtokens/8.0.1/microsoft.identitymodel.jsonwebtokens.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.logging/8.0.1/microsoft.identitymodel.logging.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.tokens/8.0.1/microsoft.identitymodel.tokens.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/rabbitmq.client/7.1.2/rabbitmq.client.7.1.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.pipelines/8.0.0/system.io.pipelines.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.ratelimiting/8.0.0/system.threading.ratelimiting.8.0.0.nupkg.sha512"], "logs": []}